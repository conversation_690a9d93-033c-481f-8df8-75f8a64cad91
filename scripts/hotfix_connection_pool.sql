-- Immediate hotfix for connection pool and query performance issues
-- Run this script to apply immediate optimizations

-- 1. Optimize existing indexes
R<PERSON><PERSON>EX INDEX CONCURRENTLY idx_users_username;
REINDEX INDEX CONCURRENTLY idx_users_phone;

-- 2. Update table statistics
ANALYZE public.users;

-- 3. Check current connection settings
SELECT name, setting, unit, context 
FROM pg_settings 
WHERE name IN ('max_connections', 'shared_buffers', 'work_mem', 'maintenance_work_mem');

-- 4. Monitor current connection usage
SELECT 
    state,
    count(*) as connections,
    round(100.0 * count(*) / (SELECT setting::int FROM pg_settings WHERE name = 'max_connections'), 2) as percentage
FROM pg_stat_activity 
GROUP BY state
ORDER BY connections DESC;

-- 5. Check for long-running queries
SELECT 
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query,
    state
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
AND state != 'idle'
ORDER BY duration DESC;

-- 6. Check for lock waits
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;

-- 7. Temporary connection limit increase (if needed)
-- Uncomment and adjust if you need immediate relief
-- ALTER SYSTEM SET max_connections = 300;
-- SELECT pg_reload_conf();

-- 8. Check index usage on users table
SELECT 
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE tablename = 'users'
ORDER BY idx_scan DESC;

-- 9. Check table bloat
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    round(100.0 * n_dead_tup / NULLIF(n_live_tup + n_dead_tup, 0), 2) as dead_tuple_percent
FROM pg_stat_user_tables 
WHERE tablename = 'users';

-- 10. Recommendations based on current state
DO $$
DECLARE
    max_conn_setting int;
    current_connections int;
    dead_tuple_pct numeric;
BEGIN
    -- Get current max connections
    SELECT setting::int INTO max_conn_setting FROM pg_settings WHERE name = 'max_connections';
    
    -- Get current active connections
    SELECT count(*) INTO current_connections FROM pg_stat_activity WHERE state = 'active';
    
    -- Get dead tuple percentage
    SELECT round(100.0 * n_dead_tup / NULLIF(n_live_tup + n_dead_tup, 0), 2) 
    INTO dead_tuple_pct 
    FROM pg_stat_user_tables 
    WHERE tablename = 'users';
    
    RAISE NOTICE 'Current max_connections: %', max_conn_setting;
    RAISE NOTICE 'Current active connections: %', current_connections;
    RAISE NOTICE 'Connection utilization: %%%', round(100.0 * current_connections / max_conn_setting, 2);
    
    IF dead_tuple_pct > 20 THEN
        RAISE NOTICE 'WARNING: Users table has % dead tuples. Consider running VACUUM.', dead_tuple_pct;
    END IF;
    
    IF current_connections > (max_conn_setting * 0.8) THEN
        RAISE NOTICE 'WARNING: Connection pool is % utilized. Consider increasing max_connections or optimizing queries.', 
                     round(100.0 * current_connections / max_conn_setting, 2);
    END IF;
END $$;
