use anyhow::{Context, Result};
use chrono::{DateTime, Utc};
use sqlx::{PgPool, Postgres, QueryBuilder, Row};
use uuid::Uuid;

use crate::service::user::user_service::GetUsersQuery;

/// 用户数据访问层
pub struct UserRepository;

impl UserRepository {
    /// 获取所有用户的基础信息
    pub async fn fetch_all_users(pool: &PgPool) -> Result<Vec<sqlx::postgres::PgRow>> {
        let user_records = sqlx::query(
            r#"
            SELECT u.id, u.phone_number, u.created_at, u.username, u.phone_verified, u.is_active
            FROM public.users u
            WHERE u.is_active = true
            ORDER BY u.created_at DESC
            "#,
        )
        .fetch_all(pool)
        .await
        .context("Failed to fetch users")?;

        Ok(user_records)
    }

    /// 获取所有活跃租户的schema名称
    pub async fn fetch_active_tenant_schemas(pool: &PgPool) -> Result<Vec<String>> {
        let tenant_schemas = sqlx::query!(
            r#"
            SELECT schema_name
            FROM public.tenants
            WHERE status = 'active'
            "#
        )
        .fetch_all(pool)
        .await
        .context("Failed to fetch tenant schemas")?;

        Ok(tenant_schemas.into_iter().map(|t| t.schema_name).collect())
    }

    /// 查询用户在指定租户中的角色
    pub async fn fetch_user_roles_in_tenant(
        pool: &PgPool,
        user_id: Uuid,
        schema_name: &str,
    ) -> Result<Vec<String>> {
        let query = format!(
            r#"
            SELECT r.code as role_code
            FROM "{schema_name}".user_identities ui
            JOIN public.roles r ON ui.role_id = r.id
            WHERE ui.user_id = $1
            "#,
            schema_name = schema_name
        );

        let role_records = sqlx::query(&query)
            .bind(user_id)
            .fetch_all(pool)
            .await
            .unwrap_or_else(|_| Vec::new()); // Skip if schema doesn't exist

        let mut roles = Vec::new();
        for record in role_records {
            let role_code: String = record.get("role_code");
            roles.push(role_code);
        }

        Ok(roles)
    }

    /// 分页查询用户（支持搜索条件）
    pub async fn fetch_users_paginated(
        pool: &PgPool,
        query: &GetUsersQuery,
    ) -> Result<(Vec<sqlx::postgres::PgRow>, i64), sqlx::Error> {
        let page = query.page.unwrap_or(1).max(1);
        let per_page = query.per_page.unwrap_or(20).min(100);
        let offset = ((page - 1) * per_page) as i64;
        let limit = per_page as i64;

        // 构建基础查询条件
        let mut where_conditions = vec!["u.is_active = true".to_string()];
        let mut bind_values: Vec<String> = Vec::new();

        // 搜索条件
        if let Some(search) = &query.search {
            if !search.trim().is_empty() {
                where_conditions.push("(u.username ILIKE $1 OR u.phone_number ILIKE $1)".to_string());
                bind_values.push(format!("%{}%", search.trim()));
            }
        }

        let where_clause = if where_conditions.len() > 1 {
            format!("WHERE {}", where_conditions.join(" AND "))
        } else {
            format!("WHERE {}", where_conditions[0])
        };

        // 获取总数
        let total_query = format!(
            r#"
            SELECT COUNT(*)
            FROM public.users u
            {}
            "#,
            where_clause
        );

        let total: i64 = if bind_values.is_empty() {
            sqlx::query_scalar(&total_query)
                .fetch_one(pool)
                .await?
        } else {
            sqlx::query_scalar(&total_query)
                .bind(&bind_values[0])
                .fetch_one(pool)
                .await?
        };

        // 获取分页数据
        let users_query = if bind_values.is_empty() {
            format!(
                r#"
                SELECT u.id, u.phone_number, u.created_at, u.username, u.phone_verified, u.is_active
                FROM public.users u
                {}
                ORDER BY u.created_at DESC
                LIMIT $1 OFFSET $2
                "#,
                where_clause
            )
        } else {
            format!(
                r#"
                SELECT u.id, u.phone_number, u.created_at, u.username, u.phone_verified, u.is_active
                FROM public.users u
                {}
                ORDER BY u.created_at DESC
                LIMIT $2 OFFSET $3
                "#,
                where_clause
            )
        };

        let user_records = if bind_values.is_empty() {
            sqlx::query(&users_query)
                .bind(limit)
                .bind(offset)
                .fetch_all(pool)
                .await?
        } else {
            sqlx::query(&users_query)
                .bind(&bind_values[0])
                .bind(limit)
                .bind(offset)
                .fetch_all(pool)
                .await?
        };

        Ok((user_records, total))
    }

    /// 检查用户是否存在
    pub async fn user_exists(pool: &PgPool, user_id: Uuid) -> Result<bool> {
        let result = sqlx::query!(
            "SELECT id FROM public.users WHERE id = $1 AND is_active = true",
            user_id
        )
        .fetch_optional(pool)
        .await
        .context("Failed to check user existence")?;

        Ok(result.is_some())
    }

    /// 检查用户名是否存在
    pub async fn username_exists(pool: &PgPool, username: &str) -> Result<bool> {
        let exists: bool =
            sqlx::query_scalar("SELECT EXISTS (SELECT 1 FROM public.users WHERE username = $1)")
                .bind(username)
                .fetch_one(pool)
                .await
                .context("Failed to check username existence")?;

        Ok(exists)
    }

    /// 检查手机号是否存在
    pub async fn phone_exists(pool: &PgPool, phone: &str) -> Result<bool, sqlx::Error> {
        let exists: bool = sqlx::query_scalar(
            "SELECT EXISTS (SELECT 1 FROM public.users WHERE phone_number = $1)",
        )
        .bind(phone)
        .fetch_one(pool)
        .await?;

        Ok(exists)
    }

    /// 检查手机号是否被其他用户占用
    pub async fn phone_exists_exclude_user(pool: &PgPool, phone: &str, user_id: Uuid) -> Result<bool> {
        let exists: bool = sqlx::query_scalar(
            "SELECT EXISTS (SELECT 1 FROM public.users WHERE phone_number = $1 AND id != $2)",
        )
        .bind(phone)
        .bind(user_id)
        .fetch_one(pool)
        .await
        .context("Failed to check phone existence")?;

        Ok(exists)
    }

    /// 插入新用户
    pub async fn insert_user(
        pool: &PgPool,
        username: &str,
        phone: &str,
        password_hash: &str,
        salt: &str,
    ) -> Result<(Uuid, DateTime<Utc>, String)> {
        let record: (Uuid, DateTime<Utc>, String) = sqlx::query_as(
            r#"
            INSERT INTO public.users (username, phone_number, password_hash, salt)
            VALUES ($1, $2, $3, $4)
            RETURNING id, created_at, username
            "#,
        )
        .bind(username)
        .bind(phone)
        .bind(password_hash)
        .bind(salt)
        .fetch_one(pool)
        .await
        .context("Failed to insert user")?;

        Ok(record)
    }

    /// 获取用户基本信息（用于检查是否为admin）
    pub async fn fetch_user_basic_info(pool: &PgPool, user_id: Uuid) -> Result<Option<(String, String)>> {
        let result = sqlx::query!(
            "SELECT username, phone_number FROM public.users WHERE id = $1",
            user_id
        )
        .fetch_optional(pool)
        .await
        .context("Failed to fetch user basic info")?;

        Ok(result.map(|r| (r.username, r.phone_number)))
    }

    /// 检查用户是否存在且获取admin状态
    pub async fn check_user_exists_and_admin_status(pool: &PgPool, user_id: Uuid) -> Result<(bool, Option<bool>)> {
        let result: (bool, Option<bool>) = sqlx::query_as(
            r#"
            SELECT
                EXISTS (SELECT 1 FROM public.users WHERE id = $1) AS user_exists,
                (SELECT username = 'admin' FROM public.users WHERE id = $1) AS is_admin
            "#,
        )
        .bind(user_id)
        .fetch_one(pool)
        .await
        .context("Failed to check user existence and admin status")?;

        Ok(result)
    }

    /// 更新用户手机号
    pub async fn update_user_phone(pool: &PgPool, user_id: Uuid, phone: &str) -> Result<()> {
        sqlx::query("UPDATE public.users SET phone_number = $1 WHERE id = $2")
            .bind(phone)
            .bind(user_id)
            .execute(pool)
            .await
            .context("Failed to update user phone")?;

        Ok(())
    }

    /// 更新用户密码
    pub async fn update_user_password(pool: &PgPool, user_id: Uuid, password_hash: &str, salt: &str) -> Result<()> {
        sqlx::query("UPDATE public.users SET password_hash = $1, salt = $2 WHERE id = $3")
            .bind(password_hash)
            .bind(salt)
            .bind(user_id)
            .execute(pool)
            .await
            .context("Failed to update user password")?;

        Ok(())
    }

    /// 更新用户激活状态
    pub async fn update_user_active_status(pool: &PgPool, user_id: Uuid, is_active: bool) -> Result<()> {
        sqlx::query("UPDATE public.users SET is_active = $1 WHERE id = $2")
            .bind(is_active)
            .bind(user_id)
            .execute(pool)
            .await
            .context("Failed to update user active status")?;

        Ok(())
    }

    /// 批量更新用户信息（使用 QueryBuilder）
    pub async fn update_user_fields(
        pool: &PgPool,
        user_id: Uuid,
        phone: Option<&str>,
        password_hash: Option<&str>,
        salt: Option<&str>,
        is_active: Option<bool>,
    ) -> Result<()> {
        let mut builder: QueryBuilder<Postgres> = QueryBuilder::new("UPDATE public.users SET ");
        let mut update_count = 0;

        if let Some(phone_val) = phone {
            if update_count > 0 {
                builder.push(", ");
            }
            builder.push("phone_number = ").push_bind(phone_val);
            update_count += 1;
        }

        if let Some(password_hash_val) = password_hash {
            if let Some(salt_val) = salt {
                if update_count > 0 {
                    builder.push(", ");
                }
                builder.push("password_hash = ").push_bind(password_hash_val);
                builder.push(", salt = ").push_bind(salt_val);
                update_count += 2;
            }
        }

        if let Some(active_val) = is_active {
            if update_count > 0 {
                builder.push(", ");
            }
            builder.push("is_active = ").push_bind(active_val);
            update_count += 1;
        }

        if update_count == 0 {
            return Ok(()); // 没有需要更新的字段
        }

        builder.push(" WHERE id = ").push_bind(user_id);

        builder.build().execute(pool).await
            .context("Failed to update user fields")?;

        Ok(())
    }

    /// 根据用户ID获取完整用户信息（包含认证相关字段）
    pub async fn get_user_by_id_with_auth(pool: &PgPool, user_id: Uuid) -> Result<Option<crate::model::user::auth::User>, sqlx::Error> {
        let user = sqlx::query_as::<_, crate::model::user::auth::User>(
            "SELECT id, username, phone_number, phone_verified, phone_verified_at, password_hash, salt, created_at, updated_at, last_login_at, is_active, failed_login_attempts, locked_until FROM public.users WHERE id = $1"
        )
        .bind(user_id)
        .fetch_optional(pool)
        .await?;
        Ok(user)
    }

    /// 根据手机号获取完整用户信息（包含认证相关字段）
    pub async fn get_user_by_phone_with_auth(pool: &PgPool, phone: &str) -> Result<Option<crate::model::user::auth::User>, sqlx::Error> {
        let user = sqlx::query_as::<_, crate::model::user::auth::User>(
            "SELECT id, username, phone_number, phone_verified, phone_verified_at, password_hash, salt, created_at, updated_at, last_login_at, is_active, failed_login_attempts, locked_until FROM public.users WHERE phone_number = $1"
        )
        .bind(phone)
        .fetch_optional(pool)
        .await?;
        Ok(user)
    }

    /// 根据用户名获取完整用户信息（包含认证相关字段）
    pub async fn get_user_by_username_with_auth(pool: &PgPool, username: &str) -> Result<Option<crate::model::user::auth::User>, sqlx::Error> {
        // 首先检查username列是否存在
        let column_exists_result = sqlx::query(
            "SELECT EXISTS (
                SELECT 1
                FROM information_schema.columns
                WHERE table_schema = 'public'
                AND table_name = 'users'
                AND column_name = 'username'
            )",
        )
        .fetch_one(pool)
        .await;

        if let Err(_) = column_exists_result {
            return Ok(None);
        }

        let column_exists: bool = column_exists_result?.get(0);
        if !column_exists {
            return Ok(None);
        }

        let user_result = sqlx::query_as::<_, crate::model::user::auth::User>(
            "SELECT id, username, phone_number, phone_verified, phone_verified_at, password_hash, salt, created_at, updated_at, last_login_at, is_active, failed_login_attempts, locked_until FROM public.users WHERE username = $1"
        )
        .bind(username)
        .fetch_optional(pool)
        .await;

        match user_result {
            Ok(user) => Ok(user),
            Err(_) => Ok(None),
        }
    }

    /// 创建新用户（完整版本，包含认证相关字段）
    pub async fn create_user_with_auth(
        pool: &PgPool,
        user_id: Uuid,
        phone: &str,
        phone_verified: bool,
        phone_verified_at: Option<DateTime<Utc>>,
        password_hash: &str,
        salt: &str,
        username: String,
    ) -> Result<(), sqlx::Error> {
        let now = Utc::now();
        
        sqlx::query!(
            r#"
            INSERT INTO public.users
            (id, phone_number, phone_verified, phone_verified_at, password_hash, salt, username, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            "#,
            user_id,
            phone,
            phone_verified,
            phone_verified_at,
            password_hash,
            salt,
            username,
            now,
            now
        )
        .execute(pool)
        .await?;
        Ok(())
    }

    /// 增加登录失败次数并可能锁定账户
    pub async fn increment_failed_attempts(pool: &PgPool, user_id: Uuid, lock_duration_minutes: i64) -> Result<i32, sqlx::Error> {
        let result = sqlx::query!(
            r#"
            UPDATE public.users
            SET failed_login_attempts = failed_login_attempts + 1,
                locked_until = CASE
                    WHEN failed_login_attempts + 1 >= 10 THEN $2
                    ELSE locked_until
                END
            WHERE id = $1
            RETURNING failed_login_attempts
            "#,
            user_id,
            Utc::now() + chrono::Duration::minutes(lock_duration_minutes)
        )
        .fetch_one(pool)
        .await?;
        Ok(result.failed_login_attempts.unwrap_or(0))
    }

    /// 重置登录失败次数并更新最后登录时间
    pub async fn reset_failed_attempts_and_update_login(pool: &PgPool, user_id: Uuid) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"
            UPDATE public.users
            SET failed_login_attempts = 0, locked_until = NULL, last_login_at = $1
            WHERE id = $2
            "#,
            Utc::now(),
            user_id
        )
        .execute(pool)
        .await?;
        Ok(())
    }

    /// 更新用户密码并重置安全相关字段
    pub async fn update_password_and_reset_security(
        pool: &PgPool,
        user_id: Uuid,
        password_hash: &str,
        salt: &str,
    ) -> Result<(), sqlx::Error> {
        let now = Utc::now();
        sqlx::query!(
            r#"
            UPDATE public.users
            SET password_hash = $1, salt = $2, updated_at = $3, failed_login_attempts = 0, locked_until = NULL
            WHERE id = $4
            "#,
            password_hash,
            salt,
            now,
            user_id
        )
        .execute(pool)
        .await?;
        Ok(())
    }
}
