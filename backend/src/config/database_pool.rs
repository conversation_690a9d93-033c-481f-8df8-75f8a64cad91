use sqlx::{PgPool, postgres::PgPoolOptions};
use std::time::Duration;
use anyhow::Result;

/// Database connection pool configuration optimized for bulk operations
pub struct DatabasePoolConfig {
    pub regular_pool: PgPool,
    pub bulk_import_pool: PgPool,
}

impl DatabasePoolConfig {
    /// Create optimized connection pools for different operation types
    pub async fn new(database_url: &str) -> Result<Self> {
        // Regular operations pool - smaller, faster acquisition
        let regular_pool = PgPoolOptions::new()
            .max_connections(15)
            .min_connections(5)
            .acquire_timeout(Duration::from_secs(10))
            .idle_timeout(Some(Duration::from_secs(300)))
            .max_lifetime(Some(Duration::from_secs(1800)))
            .test_before_acquire(false) // Skip health checks for speed
            .connect(database_url)
            .await?;

        // Bulk import pool - larger, longer timeouts, optimized for bulk operations
        let bulk_import_pool = PgPoolOptions::new()
            .max_connections(25) // Increased for bulk operations
            .min_connections(10)
            .acquire_timeout(Duration::from_secs(30)) // Longer timeout for bulk ops
            .idle_timeout(Some(Duration::from_secs(600))) // Keep connections longer
            .max_lifetime(Some(Duration::from_secs(3600))) // Longer lifetime
            .test_before_acquire(false)
            .connect(database_url)
            .await?;

        Ok(Self {
            regular_pool,
            bulk_import_pool,
        })
    }

    /// Get the appropriate pool for the operation type
    pub fn get_pool(&self, is_bulk_operation: bool) -> &PgPool {
        if is_bulk_operation {
            &self.bulk_import_pool
        } else {
            &self.regular_pool
        }
    }
}

/// Connection pool settings for different environments
pub struct PoolSettings;

impl PoolSettings {
    /// Production settings - optimized for high throughput
    pub fn production() -> (u32, u32, Duration, Duration) {
        (30, 15, Duration::from_secs(30), Duration::from_secs(60))
    }

    /// Development settings - smaller pools, faster timeouts
    pub fn development() -> (u32, u32, Duration, Duration) {
        (10, 5, Duration::from_secs(10), Duration::from_secs(30))
    }

    /// Bulk import settings - specialized for import operations
    pub fn bulk_import() -> (u32, u32, Duration, Duration) {
        (40, 20, Duration::from_secs(60), Duration::from_secs(120))
    }
}
