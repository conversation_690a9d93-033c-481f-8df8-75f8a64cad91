-- Optimize user table indexes for better performance
-- Migration: 20250915_optimize_user_indexes

-- Ensure username index exists and is optimized
CREATE INDEX IF NOT EXISTS idx_users_username ON public.users(username);

-- Add composite index for common query patterns
CREATE INDEX IF NOT EXISTS idx_users_username_active ON public.users(username, is_active) WHERE is_active = true;

-- Add index for phone number lookups
CREATE INDEX IF NOT EXISTS idx_users_phone_active ON public.users(phone_number, is_active) WHERE is_active = true;

-- Add partial index for active users only (most common queries)
CREATE INDEX IF NOT EXISTS idx_users_active_only ON public.users(id, username, phone_number) WHERE is_active = true;

-- Analyze tables to update statistics
ANALYZE public.users;
