# Database Connection Pool Optimization Configuration
# Apply these settings to resolve connection pool exhaustion and slow queries

[database.regular_operations]
# Standard operations pool - optimized for typical CRUD operations
max_connections = 15
min_connections = 5
acquire_timeout_seconds = 10
idle_timeout_seconds = 300
max_lifetime_seconds = 1800
test_before_acquire = false

[database.bulk_import]
# Bulk import pool - optimized for high-throughput batch operations
max_connections = 30
min_connections = 15
acquire_timeout_seconds = 30
idle_timeout_seconds = 600
max_lifetime_seconds = 3600
test_before_acquire = false

[database.performance]
# PostgreSQL performance settings recommendations
# Add these to your postgresql.conf file

# Connection settings
max_connections = 200
shared_buffers = "256MB"
effective_cache_size = "1GB"
work_mem = "4MB"
maintenance_work_mem = "64MB"

# Checkpoint settings
checkpoint_completion_target = 0.9
wal_buffers = "16MB"
default_statistics_target = 100

# Query optimization
random_page_cost = 1.1
effective_io_concurrency = 200

[logging]
# Monitoring settings to track performance improvements
slow_query_threshold_seconds = 1.0
slow_acquire_threshold_seconds = 2.0
log_statement_stats = true
log_lock_waits = true

[bulk_import_settings]
# Application-level settings for bulk imports
batch_size = 50
max_concurrent_batches = 4
inter_batch_delay_ms = 10
use_transactions = true
enable_bulk_optimization = true

# Performance monitoring queries
[monitoring.queries]
slow_queries = """
SELECT query, mean_exec_time, calls, total_exec_time
FROM pg_stat_statements 
WHERE query LIKE '%users%' 
ORDER BY mean_exec_time DESC 
LIMIT 10;
"""

connection_pool_status = """
SELECT state, count(*) as count
FROM pg_stat_activity 
GROUP BY state;
"""

index_usage = """
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read
FROM pg_stat_user_indexes 
WHERE tablename = 'users'
ORDER BY idx_scan DESC;
"""

lock_waits = """
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement,
       blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
"""
